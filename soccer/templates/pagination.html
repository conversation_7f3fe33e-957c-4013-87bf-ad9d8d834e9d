{% if page_obj.paginator.num_pages > 1 %}
  <nav>
    <ul class="pagination justify-content-center mt-4">
      {% if page_obj.has_previous %}
        <li class="page-item">
          <a class="page-link" href="?name={{ name_query }}&type={{ type_query }}&page={{ page_obj.previous_page_number }}" aria-label="Previous"><span aria-hidden="true">&laquo;</span></a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link">&laquo;</span>
        </li>
      {% endif %}

      {% for num in page_obj.paginator.page_range %}
        {% if num == page_obj.number %}
          <li class="page-item active">
            <span class="page-link">{{ num }}</span>
          </li>
        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
          <li class="page-item">
            <a class="page-link" href="?name={{ name_query }}&type={{ type_query }}&page={{ num }}">{{ num }}</a>
          </li>
        {% endif %}
      {% endfor %}

      {% if page_obj.has_next %}
        <li class="page-item">
          <a class="page-link" href="?name={{ name_query }}&type={{ type_query }}&page={{ page_obj.next_page_number }}" aria-label="Next"><span aria-hidden="true">&raquo;</span></a>
        </li>
      {% else %}
        <li class="page-item disabled">
          <span class="page-link">&raquo;</span>
        </li>
      {% endif %}
    </ul>
  </nav>
{% endif %}
