<!DOCTYPE html>
{% load i18n %}
<html lang="en">
  <head>
    {% block title %}
      <title>{% trans 'Soccer Fields Management' %}</title>
    {% endblock %}
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    {% load static %}
    <link rel="stylesheet" href="{% static 'css/bootstrap.min.css' %}" />
    <link rel="stylesheet" href="{% static 'css/bootstrap-icons.css' %}" />
    <link rel="stylesheet" href="{% static 'css/styles.css' %}" />
  </head>

  <body class="bg-light min-h-100">
    <header class="fixed-top bg-white shadow-sm">
      <div class="container-fluid px-4 py-2 d-flex align-items-center justify-content-between">
        <span class="fw-bold fs-5">{% trans 'Soccer Fields Management' %}</span>
        <div class="row align-items-center">
          <div class="col-auto">
            {% include 'language_selector.html' %}
          </div>
          {% if user.is_authenticated %}
            <div class="col-auto">
              <form action="{% url 'logout' %}" method="post" class="logout mb-0">
                {% csrf_token %}
                <button class="btn btn-outline-danger btn-sm">{% trans 'Logout' %}</button>
              </form>
            </div>
          {% endif %}
        </div>
      </div>
    </header>

    <div class="container my-4">
      {% block content %}

      {% endblock %}
    </div>

    <script src="{% static 'js/bootstrap.bundle.min.js' %}"></script>
    {% block custom_js %}

    {% endblock %}
  </body>
</html>
