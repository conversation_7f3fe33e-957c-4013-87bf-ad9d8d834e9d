{% extends 'base_generic.html' %}
{% load i18n %}
{% load humanize %}

{% block content %}
  <div class="container py-4">
    <h3 class="mb-4 text-center">{% trans 'Available Vouchers' %}</h3>
    <div class="table-responsive">
      <table class="table table-hover table-bordered align-middle">
        <thead class="table-light">
          <tr>
            <th>#</th>
            <th>
              {% trans 'Code' %}
            </th>
            <th>
              {% trans 'Description' %}
            </th>
            <th>
              {% trans 'Discount' %}(%)
            </th>
            <th>
              {% trans 'Min price' %}
            </th>
            <th>
              {% trans 'Max discount' %}
            </th>
            <th>
              {% trans 'Valid' %}
            </th>
            <th>
              {% trans 'Rest quantity' %}
            </th>
            <th>
              {% trans 'Status' %}
            </th>
          </tr>
        </thead>
        <tbody>
          {% for v in vouchers %}
            <tr>
              <td>{{ forloop.counter }}</td>
              <td>
                <b>{{ v.code }}</b>
              </td>
              <td class="small">{{ v.description|default:'-' }}</td>
              <td>{{ v.discount_percent }}</td>
              <td>{{ v.min_price|floatformat:0|intcomma }} VND</td>
              <td>{{ v.max_discount_amount|floatformat:0|intcomma }} VND</td>
              <td>{{ v.valid_from|date:DATE_FORMAT }} - {{ v.valid_to|date:DATE_FORMAT }}</td>
              <td>{{ v.rest_quantity }}</td>
              <td>
                {% if v.valid_to < now %}
                  <span class="badge bg-secondary">{% trans 'Expired' %}</span>
                {% elif v.rest_quantity == 0 %}
                  <span class="badge bg-warning">{% trans 'Out of stock' %}</span>
                {% else %}
                  <span class="badge bg-success">{% trans 'Available' %}</span>
                {% endif %}
              </td>
            </tr>
          {% empty %}
            <tr>
              <td colspan="9" class="text-center">
                {% trans 'No vouchers found.' %}
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    </div>
  </div>
{% endblock %}
