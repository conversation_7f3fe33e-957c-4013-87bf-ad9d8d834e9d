{% extends 'base_generic.html' %}
{% load base_extras %}
{% load humanize %}
{% load i18n %}
{% load static %}

{% block content %}
<div class="container py-4">
  <h3 class="mb-4"><i class="bi bi-collection me-2"></i>{% trans "All Soccer Fields" %}</h3>
  <a href="{% url 'admin_add_field' %}" class="btn btn-success mb-3">
    <i class="bi bi-plus-lg me-1"></i> {% trans "Add Soccer Field" %}
  </a>

  <table class="table table-hover align-middle table-bordered">
    <thead class="table-light">
      <tr>
        <th>#</th>
        <th>{% trans 'Name' %}</th>
        <th>{% trans 'Type' %}</th>
        <th>{% trans 'Address' %}</th>
        <th>{% trans 'Phone' %}</th>
        <th>{% trans 'Email' %}</th>
        <th>{% trans 'Price/hour' %}</th>
        <th>{% trans 'Status' %}</th>
        <th>{% trans 'Image' %}</th>
        <th class="text-end">{% trans 'Actions' %}</th>
      </tr>
    </thead>
    <tbody>
      {% for f in fields %}
        <tr {% if f.deleted_at %}class="table-danger"{% endif %}>
          <td>{{ forloop.counter }}</td>
          <td>{{ f.name|dash_if_none }}</td>
          <td>{{ f.get_type_display|dash_if_none }}</td>
          <td>{{ f.address|dash_if_none }}</td>
          <td>{{ f.phone|dash_if_none }}</td>
          <td>{{ f.email|dash_if_none }}</td>
          <td>{{ f.price_per_hour|floatformat:0|dash_if_none|intcomma }}₫</td>
          <td>
            {% if f.deleted_at %}
              <span class="badge bg-danger">{% trans "Deleted" %}</span>
            {% else %}
              <span class="badge {% if f.status == soccer_field_status.ACTIVE %}bg-success{% else %}bg-secondary{% endif %}">
                {{ f.get_status_display }}
              </span>
            {% endif %}
          </td>
          <td>
            {% if f.image %}
              <img src="{{ f.image.url }}" style="max-width: 60px; max-height: 60px;" class="rounded shadow-sm" alt="field">
            {% else %}
              <span class="text-muted">-</span>
            {% endif %}
          </td>
          <td class="text-end">
            <a href="{% url 'admin_field_detail' f.pk %}" class="btn btn-sm btn-info">
              <i class="bi bi-eye me-1"></i>{% trans "Detail" %}
            </a>
            <a href="{% url 'admin_edit_field' f.pk %}" class="btn btn-sm btn-warning">
              <i class="bi bi-pencil me-1"></i>{% trans "Edit" %}
            </a>
          </td>
        </tr>
      {% empty %}
        <tr>
          <td colspan="10" class="text-center text-muted">
            <i class="bi bi-exclamation-circle me-1"></i> {% trans "No soccer fields found." %}
          </td>
        </tr>
      {% endfor %}
    </tbody>
  </table>
</div>
{% endblock %}
