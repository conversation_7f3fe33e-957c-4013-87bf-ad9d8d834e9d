{% extends 'base_generic.html' %}
{% load humanize %}
{% load i18n %}

{% block content %}
  <div class="container py-4 max-w-720">
    <h3 class="mb-4 text-center">{% trans 'My Orders' %}</h3>
    {% if orders %}
      <table class="table table-bordered align-middle">
        <thead>
          <tr>
            <th>
              {% trans 'Field' %}
            </th>
            <th>
              {% trans 'Date' %}
            </th>
            <th>
              {% trans 'Time' %}
            </th>
            <th>
              {% trans 'Duration (min)' %}
            </th>
            <th>
              {% trans 'Status' %}
            </th>
            <th>
              {% trans 'Action' %}
            </th>
          </tr>
        </thead>
        <tbody>
          {% for order in orders %}
            <tr>
              <td>{{ order.soccer_field.name }}</td>
              <td>{{ order.time|date:'d/m/Y' }}</td>
              <td>{{ order.time|time:'H:i' }}</td>
              <td>{{ order.duration }}</td>
              <td>{{ order.get_status_display }}</td>
              <td>
                <a href="{% url 'order_detail' order.pk %}" class="btn btn-sm btn-info">{% trans 'Detail' %}</a>
                {% if order.status == OrderStatus.PENDING %}
                  <a href="{% url 'order_edit' order.pk %}" class="btn btn-sm btn-primary">{% trans 'Edit' %}</a>
                  <form method="post" action="{% url 'order_cancel' order.pk %}" style="display:inline;">
                    {% csrf_token %}
                    <button type="submit" class="btn btn-sm btn-danger" onclick="return confirm('{% trans 'Are you sure you want to cancel this order?' %}');">{% trans 'Cancel' %}</button>
                  </form>
                {% endif %}
              </td>
            </tr>
          {% endfor %}
        </tbody>
      </table>
    {% else %}
      <p class="text-center">
        {% trans 'No orders yet.' %}
      </p>
    {% endif %}
  </div>
{% endblock %}
