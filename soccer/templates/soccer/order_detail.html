{% extends 'base_generic.html' %}
{% load humanize %}
{% load i18n %}

{% block content %}
  <div class="container py-4">
    <div class="row justify-content-center">
      <div class="col-md-8 col-lg-6">
        <div class="card shadow rounded-3">
          <div class="card-header bg-primary text-white">
            <h4 class="mb-0">{% trans 'Order Details' %}</h4>
          </div>
          <div class="card-body">
            <h5 class="card-title mb-3">{{ order.soccer_field.name }}</h5>
            <ul class="list-group list-group-flush mb-3">
              <li class="list-group-item">
                <b>{% trans 'Time' %}:</b> {{ order.time|date:'d/m/Y H:i' }}
              </li>
              <li class="list-group-item">
                <b>{% trans 'Total Price' %}:</b> {{ total_price|floatformat:0|intcomma }} VND
              </li>
              <li class="list-group-item">
                <b>{% trans 'Status' %}:</b> {{ order.status }}
              </li>
              <li class="list-group-item">
                <b>{% trans 'Price per hour' %}:</b> {{ order.soccer_field.price_per_hour|floatformat:0|intcomma }} VND
              </li>
              {% if order.voucher %}
                <li class="list-group-item">
                  <b>{% trans 'Voucher' %}:</b> {{ order.voucher.code }} (-{{ order.voucher.discount_percent }}%)
                </li>
              {% endif %}
              {% if order.note %}
                <li class="list-group-item">
                  <b>{% trans 'Note' %}:</b> {{ order.note }}
                </li>
              {% endif %}
            </ul>
            {% if order.status == all_statuses.CANCELLED_BY_USER or order.status == all_statuses.CANCELLED_BY_ADMIN %}
              {% if order.cancel_reason %}
                <div class="alert alert-warning mt-3 mb-0">
                  <b>{% trans 'Cancellation reason' %}:</b>
                  <span>{{ order.cancel_reason }}</span>
                </div>
              {% endif %}
            {% endif %}
            <div class="mb-2">
              <b>{% trans 'Address' %}:</b> {{ order.soccer_field.address }}
            </div>
            <div class="mb-2">
              <b>{% trans 'Type' %}:</b> {{ order.soccer_field.type }}
            </div>
            <div class="mb-2">
              <b>{% trans 'Phone' %}:</b> {{ order.soccer_field.phone }}
            </div>
            <div class="mb-2">
              <b>{% trans 'Email' %}:</b> {{ order.soccer_field.email }}
            </div>
          </div>
          <div class="card-footer text-end">
            <div class="row justify-content-end align-items-center gap-2">
              <a href="javascript:history.back()" class="btn btn-secondary mr-1"><i class="bi bi-arrow-left mr-1"></i>{% trans 'Back' %}</a>

              {% if order.status == all_statuses.COMPLETED %}
                <a href="{% url 'order_field' order.soccer_field.pk %}" class="btn btn-primary mr-1"><i class="bi bi-repeat mr-1"></i>{% trans 'Order again' %}</a>

                {% if review %}
                  <a href="{% url 'edit_review' order.soccer_field.pk %}" class="btn btn-warning"><i class="bi bi-pencil mr-1"></i>{% trans 'Edit review' %}</a>
                {% else %}
                  <a href="{% url 'review_field' order.soccer_field.pk %}" class="btn btn-success"><i class="bi bi-star mr-1"></i>{% trans 'Review field' %}</a>
                {% endif %}
              {% endif %}
            </div>

            {% if order.status == all_statuses.COMPLETED and review %}
              <div class="mt-3">
                <span class="fw-bold">{% trans 'Your review:' %}</span>
                <div>
                  {% if review.rate %}
                    <span class="badge bg-warning text-dark"><i class="bi bi-star"></i> {{ review.rate|floatformat:1 }}/5</span>
                  {% endif %}
                  {% if review.comment %}
                    <p class="mt-2">{{ review.comment }}</p>
                  {% endif %}
                </div>
              </div>
            {% endif %}
          </div>
        </div>
      </div>
    </div>
  </div>
{% endblock %}
