{% extends 'base_generic.html' %}
{% load static %}
{% load i18n %}

{% block content %}
  {% if error %}
    <div class="alert alert-danger mt-3">{{ error }}</div>
    <a href="{% url 'my_orders' %}" class="btn btn-secondary">{% trans 'Back to my orders' %}</a>
  {% else %}
    <div class="container py-4">
      <div class="row justify-content-center">
        <div class="col-md-6">
          <h3>{% trans 'Review Soccer Field' %}: {{ soccer_field.name }}</h3>
          <form method="post" class="review-form">
            {% csrf_token %}
            <div class="mb-3">
              <label class="form-label">{% trans 'Rating (1-5 stars)' %}</label>
              <div class="star-rating mb-2" style="font-size:2rem;">
                {% for i in "54321" %}
                  <input type="radio" name="rate" id="star{{ i }}" value="{{ i }}" {% if form.rate.value|stringformat:"s" == i|stringformat:"s" %}checked{% endif %} class="d-none" />
                  <label for="star{{ i }}" data-value="{{ i }}" style="cursor:pointer;">
                    <span class="text-warning">{% if form.rate.value|stringformat:"s" >= i|stringformat:"s" %}&#9733;{% else %}&#9734;{% endif %}</span>
                  </label>
                {% endfor %}
              </div>
              {% if form.rate.errors %}
                <div class="text-danger">{{ form.rate.errors }}</div>
              {% endif %}
            </div>
            <div class="mb-3">
              <label class="form-label">{% trans 'Your Comment' %}</label>
              <textarea name="comment" class="form-control" placeholder="{% trans 'Enter your comment here...' %}">{{ form.comment.value }}</textarea>
              {% if form.comment.errors %}
                <div class="text-danger">{{ form.comment.errors }}</div>
              {% endif %}
            </div>
            <button type="submit" class="btn btn-success">{% trans 'Submit review' %}</button>
            <a href="{% url 'soccer_field_detail' soccer_field.pk %}" class="btn btn-secondary">{% trans 'Cancel' %}</a>
          </form>
        </div>
      </div>
    </div>
  {% endif %}
  <script src="{% static 'js/jquery.min.js' %}"></script>
  <script src="{% static 'js/review_field.js' %}"></script>
{% endblock %}
