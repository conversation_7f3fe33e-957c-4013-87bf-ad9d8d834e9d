{% extends 'base_generic.html' %}
{% load i18n %}
{% load static %}

{% block content %}
  <div class="container py-5 max-w-480">
    <div class="alert alert-danger text-center">
      <h4 class="mb-3">{% trans 'Are you sure you want to delete this voucher?' %}</h4>
      <p class="fw-bold">{{ voucher.code }}</p>
      {% if error %}
        <div class="alert alert-danger mt-2">{{ error }}</div>
      {% endif %}
      <form id="delete-voucher-form" method="post" class="mt-3" action="">
        {% csrf_token %}
        <div class="mb-3">
          <label><b>{% trans 'Delete effective at' %}:</b></label>
          <input type="datetime-local" name="deleted_at" class="form-control" required value="{{ now|date:'Y-m-d\\TH:i' }}" />
        </div>
        <button id="btn-delete" class="btn btn-danger" type="submit">{% trans 'Delete' %}</button>
        <a href="{% url 'voucher_list_admin' %}" class="btn btn-secondary ms-2">{% trans 'Cancel' %}</a>
      </form>
    </div>
  </div>
{% endblock %}
