# Generated by Django 4.2.23 on 2025-07-23 04:57

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('soccer', '0005_review_alter_rating_unique_together_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='fieldrequest',
            name='address',
            field=models.CharField(blank=True, max_length=256, null=True),
        ),
        migrations.AddField(
            model_name='fieldrequest',
            name='description',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='fieldrequest',
            name='email',
            field=models.EmailField(blank=True, max_length=254, null=True),
        ),
        migrations.AddField(
            model_name='fieldrequest',
            name='image',
            field=models.ImageField(blank=True, null=True, upload_to='fields/requests/'),
        ),
        migrations.AddField(
            model_name='fieldrequest',
            name='name',
            field=models.Char<PERSON><PERSON>(blank=True, max_length=128, null=True),
        ),
        migrations.Add<PERSON>ield(
            model_name='fieldrequest',
            name='phone',
            field=models.CharField(blank=True, max_length=32, null=True),
        ),
        migrations.AddField(
            model_name='fieldrequest',
            name='price_per_hour',
            field=models.DecimalField(blank=True, decimal_places=2, max_digits=10, null=True),
        ),
        migrations.AddField(
            model_name='fieldrequest',
            name='soccer_field',
            field=models.ForeignKey(blank=True, help_text='The soccer field this request is related to.', null=True, on_delete=django.db.models.deletion.SET_NULL, to='soccer.soccerfield'),
        ),
        migrations.AddField(
            model_name='fieldrequest',
            name='type_field',
            field=models.CharField(blank=True, choices=[('indoor', 'Indoor'), ('outdoor', 'Outdoor')], max_length=16, null=True),
        ),
        migrations.AlterField(
            model_name='fieldrequest',
            name='status',
            field=models.CharField(choices=[('pending', 'Pending'), ('approved', 'Approved'), ('rejected', 'Rejected'), ('cancelled', 'Cancelled')], default='pending', max_length=16),
        ),
    ]
