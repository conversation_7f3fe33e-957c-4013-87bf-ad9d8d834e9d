{% extends 'auth_generic.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block content %}
  <h3 class="mb-4 text-center">{% trans 'Register' %}</h3>
  <form method="post" novalidate>
    {% csrf_token %}
    {% for field in form %}
      <div class="mb-3">
        {{ field.label_tag }}
        {{ field|add_class:'form-control' }}
        {% if field.errors %}
          <div class="text-danger small">{{ field.errors|striptags }}</div>
        {% endif %}
      </div>
    {% endfor %}
    <button type="submit" class="btn btn-primary w-100">{% trans 'Register' %}</button>
  </form>
{% endblock %}
