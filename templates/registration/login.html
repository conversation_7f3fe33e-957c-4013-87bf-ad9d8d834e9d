{% extends 'auth_generic.html' %}
{% load i18n %}
{% load widget_tweaks %}

{% block content %}
  <h3 class="mb-4 text-center">{% trans 'Login' %}</h3>
  <form method="post">
    {% csrf_token %}
    {% for field in form %}
      <div class="mb-3">
        {{ field.label_tag }}
        {{ field|add_class:'form-control' }}
        {% if field.errors %}
          <div class="text-danger small">{{ field.errors|striptags }}</div>
        {% endif %}
      </div>
    {% endfor %}
    <button type="submit" class="btn btn-primary">{% trans 'Login' %}</button>
    <p class="mt-3 text-center">
      <a href="{% url 'password_reset' %}">{% trans 'Forgot your password?' %}</a>
    </p>
  </form>
{% endblock %}
